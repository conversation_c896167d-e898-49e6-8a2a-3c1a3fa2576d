import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import {
  CreateFarmingPlanTaskDto,
  UpdateFarmingPlanTaskDto,
  AssignFarmingPlanTaskDto,
  AssignFarmingPlanTaskDtoArray,
  FilterFarmingPlanTaskDto,
  TaskManagementInfoDto,
  WarehouseItemTaskUsedDto,
  ProductionQuantityDto,
  TaskType
} from '@app/modules/seasonal-management/farming-plan-task/task/FarmingPlanTask.dto';

describe('FarmingPlanTask DTOs', () => {
  describe('CreateFarmingPlanTaskDto', () => {
    it('should validate a valid CreateFarmingPlanTaskDto', async () => {
      const validDto = {
        farming_plan_state: 'state-123',
        label: 'Test Task',
        start_date: new Date('2024-01-01'),
        end_date: new Date('2024-01-31'),
        assigned_to: 'user-123',
        status: 'Plan',
        description: 'Test task description',
        task_type: TaskType.OTHER,
        environment_template_id: 'env-template-123',
        template_id: 'template-123'
      };

      const dto = plainToClass(CreateFarmingPlanTaskDto, validDto);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0);
    });

    it('should fail validation when farming_plan_state is missing', async () => {
      const invalidDto = {
        label: 'Test Task',
        start_date: new Date('2024-01-01'),
        end_date: new Date('2024-01-31')
      };

      const dto = plainToClass(CreateFarmingPlanTaskDto, invalidDto);
      const errors = await validate(dto);

      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('farming_plan_state');
    });

    it('should validate optional fields correctly', async () => {
      const dtoWithOptionals = {
        farming_plan_state: 'state-123',
        name: 'task-123',
        label: 'Test Task',
        image: 'test-image.jpg',
        supplies_id: 'supplies-123',
        supplies: 'Test supplies',
        start_date: new Date('2024-01-01'),
        end_date: new Date('2024-01-31'),
        index: '1',
        assigned_to: 'user-123',
        status: 'Plan',
        description: 'Test description',
        enable_origin_tracing: 1,
        task_progress: 50.5,
        added_in_diary: 0,
        involved_in: 'user-456',
        involved_in_users: 'user-456,user-789',
        priority_level: 'Normal',
        involve_in_users: ['user-456', 'user-789'],
        environment_template_id: 'env-template-123',
        tag: 'test-tag',
        text_state: 'Test state',
        text_plan: 'Test plan',
        text_assign_user: 'Test assign user',
        is_template: 0,
        template_id: 'template-123',
        task_type: TaskType.SUBCULTURE,
        item_list: [],
        prod_quantity_list: [],
        todo_list: [],
        worksheet_list: [],
        previous_task_id: 'prev-task-123',
        task_chain_ids: '["task-1", "task-2"]'
      };

      const dto = plainToClass(CreateFarmingPlanTaskDto, dtoWithOptionals);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0);
    });

    it('should validate TaskType enum correctly', async () => {
      const validTaskTypes = [
        TaskType.SUBCULTURE,
        TaskType.QUALITY_INSPECTION,
        TaskType.FINAL_QUALITY_INSPECTION,
        TaskType.OTHER,
        TaskType.ENV_STOCK,
        TaskType.ENV_STEAM_POT,
        TaskType.ENV_POUR
      ];

      for (const taskType of validTaskTypes) {
        const dto = plainToClass(CreateFarmingPlanTaskDto, {
          farming_plan_state: 'state-123',
          task_type: taskType
        });
        const errors = await validate(dto);

        const taskTypeErrors = errors.filter(error => error.property === 'task_type');
        expect(taskTypeErrors).toHaveLength(0);
      }
    });

    it('should fail validation for invalid TaskType', async () => {
      const dto = plainToClass(CreateFarmingPlanTaskDto, {
        farming_plan_state: 'state-123',
        task_type: 'InvalidTaskType'
      });
      const errors = await validate(dto);

      const taskTypeErrors = errors.filter(error => error.property === 'task_type');
      expect(taskTypeErrors.length).toBeGreaterThan(0);
    });
  });

  describe('UpdateFarmingPlanTaskDto', () => {
    it('should extend CreateFarmingPlanTaskDto', () => {
      const dto = new UpdateFarmingPlanTaskDto();
      expect(dto).toBeInstanceOf(CreateFarmingPlanTaskDto);
    });

    it('should validate update data correctly', async () => {
      const updateDto = {
        status: 'In progress',
        task_progress: 75.5,
        description: 'Updated description'
      };

      const dto = plainToClass(UpdateFarmingPlanTaskDto, updateDto);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0);
    });
  });

  describe('AssignFarmingPlanTaskDto', () => {
    it('should validate a valid AssignFarmingPlanTaskDto', async () => {
      const validDto = {
        assigned_to: '123e4567-e89b-12d3-a456-************',
        template_id: 'template-123',
        department_id: 'dept-123',
        start_date: new Date('2024-01-01'),
        end_date: new Date('2024-01-31'),
        exp_quantity: 100,
        status: 'Plan',
        description: 'Test assignment',
        task_type: TaskType.OTHER
      };

      const dto = plainToClass(AssignFarmingPlanTaskDto, validDto);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0);
    });

    it('should fail validation when required fields are missing', async () => {
      const invalidDto = {
        template_id: 'template-123'
        // Missing assigned_to, department_id, start_date, end_date, exp_quantity
      };

      const dto = plainToClass(AssignFarmingPlanTaskDto, invalidDto);
      const errors = await validate(dto);

      expect(errors.length).toBeGreaterThan(0);

      const requiredFields = ['assigned_to', 'department_id', 'start_date', 'end_date', 'exp_quantity'];
      const errorProperties = errors.map(error => error.property);

      requiredFields.forEach(field => {
        expect(errorProperties).toContain(field);
      });
    });

    it('should validate UUID format for assigned_to', async () => {
      const invalidDto = {
        assigned_to: 'invalid-uuid',
        template_id: 'template-123',
        department_id: 'dept-123',
        start_date: new Date('2024-01-01'),
        end_date: new Date('2024-01-31'),
        exp_quantity: 100
      };

      const dto = plainToClass(AssignFarmingPlanTaskDto, invalidDto);
      const errors = await validate(dto);

      const uuidErrors = errors.filter(error => error.property === 'assigned_to');
      expect(uuidErrors.length).toBeGreaterThan(0);
    });

    it('should validate AssignFarmingPlanTaskDto (backward compatibility)', async () => {
      const validDto = {
        assigned_to: '123e4567-e89b-12d3-a456-************',
        template_id: 'template-123',
        department_id: 'dept-123',
        start_date: new Date('2024-01-01'),
        end_date: new Date('2024-01-31'),
        exp_quantity: 100
      };

      const dto = plainToClass(AssignFarmingPlanTaskDto, validDto);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0);
    });

    it('should validate exp_quantity as number', async () => {
      const invalidDto = {
        assigned_to: '123e4567-e89b-12d3-a456-************',
        template_id: 'template-123',
        department_id: 'dept-123',
        start_date: new Date('2024-01-01'),
        end_date: new Date('2024-01-31'),
        exp_quantity: 'not-a-number'
      };

      const dto = plainToClass(AssignFarmingPlanTaskDto, invalidDto);
      const errors = await validate(dto);

      const quantityErrors = errors.filter(error => error.property === 'exp_quantity');
      expect(quantityErrors.length).toBeGreaterThan(0);
    });

    it('should allow optional name field for updates', async () => {
      const dtoWithName = {
        name: 'existing-task-123',
        assigned_to: '123e4567-e89b-12d3-a456-************',
        template_id: 'template-123',
        department_id: 'dept-123',
        start_date: new Date('2024-01-01'),
        end_date: new Date('2024-01-31'),
        exp_quantity: 100
      };

      const dto = plainToClass(AssignFarmingPlanTaskDto, dtoWithName);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0);
    });
  });

  describe('AssignFarmingPlanTaskDtoArray', () => {
    it('should validate a valid AssignFarmingPlanTaskDtoArray', async () => {
      const validDto = {
        state_id: 'state-123',
        department_id: 'dept-123',
        production_plan_id: 'production-plan-456', // NEW: Production plan ID for allocation scoping
        tasks: [
          {
            assigned_to: '123e4567-e89b-12d3-a456-************',
            template_id: 'template-123',
            department_id: 'dept-123',
            start_date: new Date('2024-01-01'),
            end_date: new Date('2024-01-31'),
            exp_quantity: 100
          }
        ]
      };

      const dto = plainToClass(AssignFarmingPlanTaskDtoArray, validDto);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0);
    });

    it('should validate AssignFarmingPlanTaskDtoArray without production_plan_id (backward compatibility)', async () => {
      const validDto = {
        state_id: 'state-123',
        department_id: 'dept-123',
        // production_plan_id is optional and not provided
        tasks: [
          {
            assigned_to: '123e4567-e89b-12d3-a456-************',
            template_id: 'template-123',
            department_id: 'dept-123',
            start_date: new Date('2024-01-01'),
            end_date: new Date('2024-01-31'),
            exp_quantity: 100
          }
        ]
      };

      const dto = plainToClass(AssignFarmingPlanTaskDtoArray, validDto);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0);
    });

    it('should fail validation when required fields are missing', async () => {
      const invalidDto = {
        state_id: 'state-123'
        // Missing department_id and tasks
      };

      const dto = plainToClass(AssignFarmingPlanTaskDtoArray, invalidDto);
      const errors = await validate(dto);

      expect(errors.length).toBeGreaterThan(0);

      const errorProperties = errors.map(error => error.property);
      expect(errorProperties).toContain('department_id');
      expect(errorProperties).toContain('tasks');
    });

    it('should validate tasks array is not empty', async () => {
      const dtoWithEmptyTasks = {
        state_id: 'state-123',
        department_id: 'dept-123',
        tasks: []
      };

      const dto = plainToClass(AssignFarmingPlanTaskDtoArray, dtoWithEmptyTasks);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0); // Empty array is allowed by current validation
    });
  });

  describe('WarehouseItemTaskUsedDto', () => {
    it('should validate a valid WarehouseItemTaskUsedDto', async () => {
      const validDto = {
        name: 'warehouse-item-123',
        quantity: 50.5,
        description: 'Test warehouse item',
        iot_category_id: 'category-123',
        active_uom: 'kg',
        active_conversion_factor: 1.5
      };

      const dto = plainToClass(WarehouseItemTaskUsedDto, validDto);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0);
    });

    it('should fail validation when required fields are missing', async () => {
      const invalidDto = {
        description: 'Test warehouse item'
        // Missing name and quantity
      };

      const dto = plainToClass(WarehouseItemTaskUsedDto, invalidDto);
      const errors = await validate(dto);

      expect(errors.length).toBeGreaterThan(0);

      const errorProperties = errors.map(error => error.property);
      expect(errorProperties).toContain('name');
      expect(errorProperties).toContain('quantity');
    });
  });

  describe('ProductionQuantityDto', () => {
    it('should validate a valid ProductionQuantityDto', async () => {
      const validDto = {
        name: 'production-item-123',
        quantity: 100.75,
        description: 'Test production item',
        product_id: 'product-123',
        active_uom: 'pieces',
        active_conversion_factor: 2.0
      };

      const dto = plainToClass(ProductionQuantityDto, validDto);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0);
    });

    it('should fail validation when required fields are missing', async () => {
      const invalidDto = {
        description: 'Test production item'
        // Missing name and quantity
      };

      const dto = plainToClass(ProductionQuantityDto, invalidDto);
      const errors = await validate(dto);

      expect(errors.length).toBeGreaterThan(0);

      const errorProperties = errors.map(error => error.property);
      expect(errorProperties).toContain('name');
      expect(errorProperties).toContain('quantity');
    });
  });

  describe('FilterFarmingPlanTaskDto', () => {
    it('should validate a valid FilterFarmingPlanTaskDto', async () => {
      const validDto = {
        stateId: 'state-123',
        templateId: 'template-123',
        status: 'Plan',
        assignedTo: 'user-123',
        taskType: TaskType.SUBCULTURE,
        previousTaskId: 'prev-task-123',
        filters: [['status', '=', 'Plan']],
        page: 1,
        size: 10
      };

      const dto = plainToClass(FilterFarmingPlanTaskDto, validDto);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0);
    });

    it('should allow all fields to be optional', async () => {
      const emptyDto = {};

      const dto = plainToClass(FilterFarmingPlanTaskDto, emptyDto);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0);
    });
  });

  describe('TaskManagementInfoDto', () => {
    it('should validate a valid TaskManagementInfoDto', async () => {
      const validDto = {
        page: 1,
        size: 20,
        stateId: 'state-123',
        templateId: 'template-123',
        status: 'Plan',
        assignedTo: 'user-123',
        taskType: TaskType.ENV_STOCK,
        orderBy: 'creation:DESC',
        filters: [['status', '=', 'Plan']]
      };

      const dto = plainToClass(TaskManagementInfoDto, validDto);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0);
    });

    it('should allow all fields to be optional', async () => {
      const emptyDto = {};

      const dto = plainToClass(TaskManagementInfoDto, emptyDto);
      const errors = await validate(dto);

      expect(errors).toHaveLength(0);
    });

    it('should validate numeric fields correctly', async () => {
      const dtoWithInvalidNumbers = {
        page: 'not-a-number',
        size: 'also-not-a-number'
      };

      const dto = plainToClass(TaskManagementInfoDto, dtoWithInvalidNumbers);
      const errors = await validate(dto);

      const pageErrors = errors.filter(error => error.property === 'page');
      const sizeErrors = errors.filter(error => error.property === 'size');

      expect(pageErrors.length).toBeGreaterThan(0);
      expect(sizeErrors.length).toBeGreaterThan(0);
    });
  });
});
