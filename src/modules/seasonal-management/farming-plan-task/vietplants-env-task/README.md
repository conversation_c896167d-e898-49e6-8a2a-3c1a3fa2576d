# VietPlants Environment Task Module - Frontend API Integration Guide

Hướng dẫn tích hợp API cho Frontend để quản lý công việc môi trường trong hệ thống VietPlants.

## 🚀 Tổng quan

Module này cung cấp các API CRUD đầy đủ cho việc quản lý công việc farming plan trong môi trường VietPlants, với hỗ trợ đặc biệt cho các task ENV_POUR có thể liên kết với ENV_STOCK và ENV_STEAM_POT.

## 📋 Danh sách API Endpoints

### 1. Quản lý Task cơ bản

| Method | Endpoint | Mô tả |
|--------|----------|-------|
| GET | `/seasonal-management/farming-plan-task/env/task-management-info` | Lấy danh sách task với filter và phân trang |
| GET | `/seasonal-management/farming-plan-task/env/:id` | Lấy thông tin task theo ID |
| POST | `/seasonal-management/farming-plan-task/env` | Tạo task mới |
| PUT | `/seasonal-management/farming-plan-task/env/:id` | Cập nhật task |
| DELETE | `/seasonal-management/farming-plan-task/env/:id` | Xóa task |

### 2. Quản lý Task hàng loạt

| Method | Endpoint | Mô tả |
|--------|----------|-------|
| POST | `/seasonal-management/farming-plan-task/env/bulk` | Tạo nhiều task cùng lúc |
| DELETE | `/seasonal-management/farming-plan-task/env/bulk` | Xóa nhiều task cùng lúc |

### 3. Quản lý Task liên kết và đặc biệt

| Method | Endpoint | Mô tả |
|--------|----------|-------|
| GET | `/seasonal-management/farming-plan-task/env/available-source-tasks` | Lấy danh sách task nguồn có thể liên kết |
| GET | `/seasonal-management/farming-plan-task/env/:id/transfers` | Lấy thông tin chuyển giao của task |
| GET | `/seasonal-management/farming-plan-task/env/status/in-progress` | Lấy task đang thực hiện |
| POST | `/seasonal-management/farming-plan-task/env/approve-env-pour` | Phê duyệt task ENV_POUR |

## 🔧 Chi tiết API và cách sử dụng

### 1. Lấy danh sách task với filter

**Endpoint:** `GET /seasonal-management/farming-plan-task/env/task-management-info`

**Query Parameters:**
```typescript
interface TaskManagementQuery {
  page?: number;           // Trang hiện tại (mặc định: 1)
  size?: number;           // Số lượng item/trang (mặc định: 100)
  filters?: string;        // JSON string của filter array
  stateId?: string;        // ID của state
  templateId?: string;     // ID của template
  status?: string;         // Trạng thái task
  assignedTo?: string;     // ID người được giao
  taskType?: string;       // Loại task
  orderBy?: string;        // Sắp xếp theo field
}
```

**Ví dụ request:**
```javascript
// Lấy task trang 1, 20 items, filter theo status và task type
const response = await fetch('/seasonal-management/farming-plan-task/env/task-management-info?' +
  new URLSearchParams({
    page: '1',
    size: '20',
    status: 'In Progress',
    taskType: 'ENV_POUR',
    stateId: 'state-123',
    filters: JSON.stringify([
      ['name', 'ILIKE', '%chiết rót%'],
      ['created_at', '>=', '2024-01-01']
    ])
  }), {
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
```

**Response:**
```typescript
{
  data: TaskItem[],
  total: number,
  page: number,
  size: number,
  totalPages: number
}
```

### 2. Lấy thông tin task theo ID

**Endpoint:** `GET /seasonal-management/farming-plan-task/env/:id`

**Ví dụ request:**
```javascript
const taskId = 'task-uuid-123';
const response = await fetch(`/seasonal-management/farming-plan-task/env/${taskId}`, {
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  }
});

const taskData = await response.json();
```

### 3. Tạo task mới

**Endpoint:** `POST /seasonal-management/farming-plan-task/env`

**Request Body:**
```typescript
interface CreateTaskRequest {
  name?: string;
  farming_plan_state: string;        // Required
  label?: string;
  task_type?: TaskType;
  start_date?: Date;
  end_date?: Date;
  department_id?: string;
  assigned_to?: string;
  status?: string;
  description?: string;
  item_list?: WarehouseItemTaskUsedDto[];
  prod_quantity_list?: ProductionQuantityDto[];
  task_transfers?: TaskTransferDto[];  // Cho ENV_POUR tasks
}
```

**Ví dụ tạo task ENV_STOCK:**
```javascript
const newTask = {
  name: "Quản lý kho nguyên liệu",
  farming_plan_state: "state-123",
  label: "Quản lý kho chính",
  task_type: "ENV_STOCK",
  start_date: "2024-06-20T00:00:00Z",
  end_date: "2024-06-25T00:00:00Z",
  department_id: "dept-001",
  assigned_to: "user-123",
  status: "Not Started",
  description: "Quản lý và kiểm soát kho nguyên liệu",
  item_list: [
    {
      name: "Phân bón NPK",
      quantity: 10,
      active_uom: "kg",
      description: "Phân bón trong kho"
    }
  ],
  prod_quantity_list: [
    {
      name: "Nguyên liệu đã chuẩn bị",
      quantity: 100,
      active_uom: "kg",
      description: "Tổng nguyên liệu trong kho"
    }
  ]
};

const response = await fetch('/seasonal-management/farming-plan-task/env', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(newTask)
});

const result = await response.json();
```

**Ví dụ tạo task ENV_POUR với liên kết:**
```javascript
const envPourTask = {
  name: "Chiết rót sản phẩm",
  farming_plan_state: "state-123",
  label: "Chiết rót từ kho và nồi hấp",
  task_type: "ENV_POUR",
  start_date: "2024-06-20T00:00:00Z",
  end_date: "2024-06-22T00:00:00Z",
  department_id: "dept-001",
  assigned_to: "user-123",
  status: "Not Started",
  description: "Chiết rót sản phẩm từ các nguồn khác nhau",
  task_transfers: [
    {
      source_task_id: "stock-task-001",
      source_task_type: "ENV_STOCK",
      description: "Lấy nguyên liệu từ kho stock"
    },
    {
      source_task_id: "steam-task-001",
      source_task_type: "ENV_STEAM_POT",
      description: "Lấy sản phẩm từ nồi hấp"
    }
  ],
  item_list: [
    {
      name: "Chai chiết rót",
      quantity: 50,
      active_uom: "cái",
      draft_quantity: 45  // Có thể chỉnh sửa
    }
  ],
  prod_quantity_list: [
    {
      name: "Sản phẩm chiết rót",
      quantity: 100,
      active_uom: "lít",
      draft_quantity: 95  // Có thể chỉnh sửa
    }
  ]
};

const response = await fetch('/seasonal-management/farming-plan-task/env', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(envPourTask)
});
```

### 4. Cập nhật task

**Endpoint:** `PUT /seasonal-management/farming-plan-task/env/:id`

**Ví dụ cập nhật task:**
```javascript
const taskId = 'task-uuid-123';
const updateData = {
  status: "In Progress",
  task_progress: 50,
  description: "Đã hoàn thành 50% công việc",
  item_list: [
    {
      name: "Phân bón NPK",
      quantity: 8,  // Giảm từ 10 xuống 8
      draft_quantity: 7.5  // Cập nhật draft quantity
    }
  ]
};

const response = await fetch(`/seasonal-management/farming-plan-task/env/${taskId}`, {
  method: 'PUT',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(updateData)
});

const result = await response.json();
```

### 5. Xóa task

**Endpoint:** `DELETE /seasonal-management/farming-plan-task/env/:id`

**Ví dụ xóa task:**
```javascript
const taskId = 'task-uuid-123';
const response = await fetch(`/seasonal-management/farming-plan-task/env/${taskId}`, {
  method: 'DELETE',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  }
});

const result = await response.json();
```

### 6. Tạo nhiều task cùng lúc

**Endpoint:** `POST /seasonal-management/farming-plan-task/env/bulk`

**Ví dụ bulk create:**
```javascript
const bulkTasks = {
  tasks: [
    {
      name: "Task stock 1",
      farming_plan_state: "state-123",
      task_type: "ENV_STOCK",
      label: "Quản lý kho A",
      department_id: "dept-001"
    },
    {
      name: "Task steam pot 1",
      farming_plan_state: "state-123",
      task_type: "ENV_STEAM_POT",
      label: "Vận hành nồi hấp B",
      department_id: "dept-001"
    },
    {
      name: "Task pour 1",
      farming_plan_state: "state-123",
      task_type: "ENV_POUR",
      label: "Chiết rót sản phẩm C",
      department_id: "dept-001"
    }
  ]
};

const response = await fetch('/seasonal-management/farming-plan-task/env/bulk', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(bulkTasks)
});

const result = await response.json();
```

### 7. Xóa nhiều task cùng lúc

**Endpoint:** `DELETE /seasonal-management/farming-plan-task/env/bulk`

**Ví dụ bulk delete:**
```javascript
const bulkDelete = {
  task_ids: ["task-001", "task-002", "task-003"],
  reason: "Dọn dẹp các task cũ không cần thiết"
};

const response = await fetch('/seasonal-management/farming-plan-task/env/bulk', {
  method: 'DELETE',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(bulkDelete)
});

const result = await response.json();
```

### 8. Lấy danh sách task nguồn có thể liên kết

**Endpoint:** `GET /seasonal-management/farming-plan-task/env/available-source-tasks`

**Query Parameters:**
- `stateId`: ID của state (optional)
- `departmentId`: ID của department (optional)

**Ví dụ request:**
```javascript
const response = await fetch('/seasonal-management/farming-plan-task/env/available-source-tasks?' +
  new URLSearchParams({
    stateId: 'state-123',
    departmentId: 'dept-001'
  }), {
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  }
});

const availableTasks = await response.json();
// Response: Array of tasks with type ENV_STOCK and ENV_STEAM_POT
```

### 9. Lấy thông tin chuyển giao của task

**Endpoint:** `GET /seasonal-management/farming-plan-task/env/:id/transfers`

**Ví dụ request:**
```javascript
const taskId = 'env-pour-task-123';
const response = await fetch(`/seasonal-management/farming-plan-task/env/${taskId}/transfers`, {
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  }
});

const transfers = await response.json();
// Response: Array of incoming transfers for this task
```

### 10. Lấy task đang thực hiện

**Endpoint:** `GET /seasonal-management/farming-plan-task/env/status/in-progress`

**Query Parameters:**
- `page`: Trang hiện tại (mặc định: 1)
- `size`: Số lượng item/trang (mặc định: 10)
- `taskType`: Loại task (optional)

**Ví dụ request:**
```javascript
const response = await fetch('/seasonal-management/farming-plan-task/env/status/in-progress?' +
  new URLSearchParams({
    page: '1',
    size: '20',
    taskType: 'ENV_POUR'
  }), {
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  }
});

const inProgressTasks = await response.json();
```

### 11. Phê duyệt task ENV_POUR

**Endpoint:** `POST /seasonal-management/farming-plan-task/env/approve-env-pour`

**Request Body:**
```typescript
interface ApproveEnvPourRequest {
  task_id: string;           // Required
  approval_notes?: string;   // Optional
  approved_by?: string;      // Optional
}
```

**Ví dụ request:**
```javascript
const approvalData = {
  task_id: "env-pour-task-123",
  approval_notes: "Đã kiểm tra và phê duyệt hoàn thành task chiết rót",
  approved_by: "manager-user-456"
};

const response = await fetch('/seasonal-management/farming-plan-task/env/approve-env-pour', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(approvalData)
});

const result = await response.json();
// Task status will change from "In Progress" to "Completed"
```

## 📊 Data Types và Interfaces

### TaskType Enum
```typescript
enum TaskType {
  ENV_STOCK = "ENV_STOCK",
  ENV_STEAM_POT = "ENV_STEAM_POT",
  ENV_POUR = "ENV_POUR"
}
```

### WarehouseItemTaskUsedDto
```typescript
interface WarehouseItemTaskUsedDto {
  name: string;                    // Required - Tên item
  quantity: number;                // Required - Số lượng
  description?: string;            // Optional - Mô tả
  iot_category_id?: string;        // Optional - ID danh mục
  active_uom?: string;             // Optional - Đơn vị tính (1-140 chars)
  active_conversion_factor?: number; // Optional - Hệ số chuyển đổi (0.000000001-999999999)
  draft_quantity?: number;         // Optional - Số lượng draft (có thể chỉnh sửa, ≥0)
}
```

### ProductionQuantityDto
```typescript
interface ProductionQuantityDto {
  name: string;                    // Required - Tên sản phẩm
  quantity: number;                // Required - Số lượng
  description?: string;            // Optional - Mô tả
  product_id?: string;             // Optional - ID sản phẩm
  active_uom?: string;             // Optional - Đơn vị tính (1-140 chars)
  active_conversion_factor?: number; // Optional - Hệ số chuyển đổi (0.000000001-999999999)
  draft_quantity?: number;         // Optional - Số lượng draft (có thể chỉnh sửa, ≥0)
}
```

### TaskTransferDto
```typescript
interface TaskTransferDto {
  source_task_id: string;          // Required - ID task nguồn
  source_task_type: TaskType;      // Required - Loại task nguồn (ENV_STOCK hoặc ENV_STEAM_POT)
  description?: string;            // Optional - Mô tả chuyển giao
}
```

## 🎯 Các trường hợp sử dụng thực tế

### 1. Tạo quy trình sản xuất hoàn chỉnh

```javascript
// Bước 1: Tạo task quản lý kho
const stockTask = await createTask({
  name: "Quản lý kho nguyên liệu",
  farming_plan_state: "state-123",
  task_type: "ENV_STOCK",
  start_date: "2024-06-20T00:00:00Z",
  end_date: "2024-06-25T00:00:00Z"
});

// Bước 2: Tạo task vận hành nồi hấp
const steamTask = await createTask({
  name: "Vận hành nồi hấp",
  farming_plan_state: "state-123",
  task_type: "ENV_STEAM_POT",
  start_date: "2024-06-22T00:00:00Z",
  end_date: "2024-06-28T00:00:00Z"
});

// Bước 3: Tạo task chiết rót liên kết với stock và steam pot
const pourTask = await createTask({
  name: "Chiết rót sản phẩm cuối",
  farming_plan_state: "state-123",
  task_type: "ENV_POUR",
  start_date: "2024-06-28T00:00:00Z",
  end_date: "2024-06-30T00:00:00Z",
  task_transfers: [
    {
      source_task_id: stockTask.id,
      source_task_type: "ENV_STOCK",
      description: "Lấy nguyên liệu từ kho"
    },
    {
      source_task_id: steamTask.id,
      source_task_type: "ENV_STEAM_POT",
      description: "Lấy sản phẩm từ nồi hấp"
    }
  ]
});
```

### 2. Theo dõi tiến độ task

```javascript
// Lấy danh sách task đang thực hiện
const inProgressTasks = await fetch('/seasonal-management/farming-plan-task/env/status/in-progress');

// Cập nhật tiến độ task
const updateProgress = async (taskId, progress) => {
  return await fetch(`/seasonal-management/farming-plan-task/env/${taskId}`, {
    method: 'PUT',
    headers: {
      'Authorization': 'Bearer ' + token,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      task_progress: progress,
      status: progress === 100 ? "Completed" : "In Progress"
    })
  });
};
```

### 3. Quản lý task ENV_POUR với draft quantities

```javascript
// Tạo task ENV_POUR với draft quantities có thể chỉnh sửa
const envPourTask = {
  name: "Chiết rót sản phẩm thử nghiệm",
  farming_plan_state: "state-123",
  task_type: "ENV_POUR",
  item_list: [
    {
      name: "Chai thủy tinh",
      quantity: 100,        // Số lượng kế hoạch
      draft_quantity: 95,   // Số lượng thực tế có thể chỉnh sửa
      active_uom: "cái"
    }
  ],
  prod_quantity_list: [
    {
      name: "Tinh dầu chiết xuất",
      quantity: 50,         // Số lượng kế hoạch
      draft_quantity: 48,   // Số lượng thực tế có thể chỉnh sửa
      active_uom: "lít"
    }
  ]
};

// Tạo task
const createdTask = await createTask(envPourTask);

// Sau khi hoàn thành, phê duyệt task
const approval = await fetch('/seasonal-management/farming-plan-task/env/approve-env-pour', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    task_id: createdTask.id,
    approval_notes: "Đã kiểm tra và xác nhận số lượng thực tế",
    approved_by: "manager-001"
  })
});
```

## ⚠️ Lưu ý quan trọng

### 1. Authentication
Tất cả API đều yêu cầu authentication với Bearer token:
```javascript
headers: {
  'Authorization': 'Bearer ' + yourAccessToken,
  'Content-Type': 'application/json'
}
```

### 2. User Permissions
API chỉ cho phép các user types sau:
- `SYSTEM_USER`
- `VIIS_IOT_USER`

### 3. Task Linking Rules
- Chỉ task `ENV_POUR` mới có thể có `task_transfers`
- `source_task_type` trong `task_transfers` chỉ được phép là `ENV_STOCK` hoặc `ENV_STEAM_POT`
- Task nguồn phải tồn tại trước khi tạo task đích

### 4. Draft Quantities
- Chỉ task `ENV_POUR` mới có thể chỉnh sửa `draft_quantity` trong `item_list` và `prod_quantity_list`
- `draft_quantity` phải ≥ 0
- Sử dụng API approve để chuyển task từ "In Progress" sang "Completed"

### 5. Error Handling
```javascript
try {
  const response = await fetch('/api/endpoint', options);

  if (!response.ok) {
    const errorData = await response.json();
    console.error('API Error:', errorData);
    // Handle error appropriately
    return;
  }

  const data = await response.json();
  // Process successful response
} catch (error) {
  console.error('Network Error:', error);
  // Handle network errors
}
```

## 🔍 Filtering và Search

### Filter Syntax
Filters được truyền dưới dạng JSON string với cấu trúc:
```typescript
type FilterTuple = [field: string, operator: string, value: any];
```

**Các operators hỗ trợ:**
- `=`: Bằng
- `!=`: Khác
- `>`: Lớn hơn
- `>=`: Lớn hơn hoặc bằng
- `<`: Nhỏ hơn
- `<=`: Nhỏ hơn hoặc bằng
- `LIKE`: Tương tự (case sensitive)
- `ILIKE`: Tương tự (case insensitive)
- `IN`: Trong danh sách
- `NOT IN`: Không trong danh sách

**Ví dụ filters:**
```javascript
const filters = [
  ['name', 'ILIKE', '%chiết rót%'],           // Tên chứa "chiết rót"
  ['status', '=', 'In Progress'],             // Status = "In Progress"
  ['created_at', '>=', '2024-01-01'],         // Tạo từ 2024-01-01
  ['task_type', 'IN', ['ENV_POUR', 'ENV_STOCK']], // Task type trong danh sách
  ['assigned_to', '!=', null]                 // Đã được assign
];

const queryString = new URLSearchParams({
  filters: JSON.stringify(filters),
  page: '1',
  size: '20'
});
```

## 📱 Frontend Integration Examples

### React Hook Example
```typescript
import { useState, useEffect } from 'react';

const useEnvTasks = (filters = {}) => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchTasks = async () => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams({
        page: '1',
        size: '20',
        ...filters,
        filters: JSON.stringify([['task_type', 'ILIKE', '%ENV%']])
      });

      const response = await fetch(
        `/seasonal-management/farming-plan-task/env/task-management-info?${queryParams}`,
        {
          headers: {
            'Authorization': `Bearer ${getToken()}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.ok) throw new Error('Failed to fetch tasks');

      const data = await response.json();
      setTasks(data.data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTasks();
  }, [JSON.stringify(filters)]);

  return { tasks, loading, error, refetch: fetchTasks };
};

// Usage in component
const TaskList = () => {
  const { tasks, loading, error } = useEnvTasks({
    status: 'In Progress',
    taskType: 'ENV_POUR'
  });

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      {tasks.map(task => (
        <div key={task.id}>
          <h3>{task.name}</h3>
          <p>Status: {task.status}</p>
          <p>Type: {task.task_type}</p>
        </div>
      ))}
    </div>
  );
};
```

### Vue.js Composition API Example
```typescript
import { ref, reactive, onMounted } from 'vue';

export const useEnvTasks = () => {
  const tasks = ref([]);
  const loading = ref(false);
  const error = ref(null);

  const createTask = async (taskData) => {
    loading.value = true;
    try {
      const response = await fetch('/seasonal-management/farming-plan-task/env', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(taskData)
      });

      if (!response.ok) throw new Error('Failed to create task');

      const newTask = await response.json();
      tasks.value.push(newTask);
      return newTask;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updateTask = async (taskId, updateData) => {
    try {
      const response = await fetch(`/seasonal-management/farming-plan-task/env/${taskId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      if (!response.ok) throw new Error('Failed to update task');

      const updatedTask = await response.json();
      const index = tasks.value.findIndex(t => t.id === taskId);
      if (index !== -1) {
        tasks.value[index] = updatedTask;
      }
      return updatedTask;
    } catch (err) {
      error.value = err.message;
      throw err;
    }
  };

  return {
    tasks,
    loading,
    error,
    createTask,
    updateTask
  };
};
```

Hướng dẫn này cung cấp đầy đủ thông tin để frontend team có thể tích hợp thành công với VietPlants Environment Task API. Nếu có thắc mắc hoặc cần hỗ trợ thêm, vui lòng liên hệ team backend.
