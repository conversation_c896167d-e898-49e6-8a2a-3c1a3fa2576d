# Upsert Functionality Test Guide

This document provides test scenarios to verify the upsert functionality for the farming plan task CREATE API.

## Test Scenarios

### 1. Create New Task with Related Data

**Request:**
```json
POST /seasonal-management/farming-plan-task/env/
{
  "name": "test-task-001",
  "farming_plan_state": "state-123",
  "task_type": "ENV_STOCK",
  "label": "Test Stock Task",
  "description": "Initial test task",
  "item_list": [
    {
      "name": "item-001",
      "quantity": 10,
      "description": "Test item 1",
      "iot_category_id": "category-123",
      "active_uom": "kg",
      "active_conversion_factor": 1,
      "draft_quantity": 5
    }
  ],
  "prod_quantity_list": [
    {
      "name": "prod-001",
      "quantity": 20,
      "description": "Test product 1",
      "product_id": "product-123",
      "active_uom": "kg",
      "active_conversion_factor": 1,
      "draft_quantity": 10
    }
  ]
}
```

**Expected Result:** New task created with warehouse items and production quantities.

### 2. Update Existing Task (Upsert Main Task)

**Request:**
```json
POST /seasonal-management/farming-plan-task/env/
{
  "name": "test-task-001",
  "farming_plan_state": "state-123",
  "task_type": "ENV_STOCK",
  "label": "Updated Test Stock Task",
  "description": "Updated test task description",
  "item_list": [
    {
      "name": "item-001",
      "quantity": 15,
      "description": "Updated test item 1",
      "iot_category_id": "category-123",
      "active_uom": "kg",
      "active_conversion_factor": 1,
      "draft_quantity": 8
    },
    {
      "name": "item-002",
      "quantity": 25,
      "description": "New test item 2",
      "iot_category_id": "category-456",
      "active_uom": "kg",
      "active_conversion_factor": 1,
      "draft_quantity": 12
    }
  ],
  "prod_quantity_list": [
    {
      "name": "prod-001",
      "quantity": 30,
      "description": "Updated test product 1",
      "product_id": "product-123",
      "active_uom": "kg",
      "active_conversion_factor": 1,
      "draft_quantity": 15
    }
  ]
}
```

**Expected Result:** 
- Task updated with new label and description
- item-001 updated with new quantity and description
- item-002 created as new item
- prod-001 updated with new quantity and description

### 3. Delete Items by Omitting from Array

**Request:**
```json
POST /seasonal-management/farming-plan-task/env/
{
  "name": "test-task-001",
  "farming_plan_state": "state-123",
  "task_type": "ENV_STOCK",
  "label": "Updated Test Stock Task",
  "description": "Updated test task description",
  "item_list": [
    {
      "name": "item-002",
      "quantity": 30,
      "description": "Only remaining item",
      "iot_category_id": "category-456",
      "active_uom": "kg",
      "active_conversion_factor": 1,
      "draft_quantity": 15
    }
  ],
  "prod_quantity_list": []
}
```

**Expected Result:**
- item-001 deleted (not in array)
- item-002 updated
- All production quantities deleted (empty array)

### 4. ENV_POUR Task with Task Transfers

**Request:**
```json
POST /seasonal-management/farming-plan-task/env/
{
  "name": "test-pour-task-001",
  "farming_plan_state": "state-123",
  "task_type": "ENV_POUR",
  "label": "Test Pour Task",
  "description": "Test ENV_POUR task with transfers",
  "task_transfers": [
    {
      "source_task_id": "test-task-001",
      "source_task_type": "ENV_STOCK",
      "description": "Transfer from stock task"
    }
  ],
  "item_list": [
    {
      "name": "pour-item-001",
      "quantity": 5,
      "description": "Pour item 1",
      "iot_category_id": "category-789",
      "active_uom": "kg",
      "active_conversion_factor": 1,
      "draft_quantity": 2
    }
  ]
}
```

**Expected Result:** ENV_POUR task created with task transfers and warehouse items.

### 5. Update ENV_POUR Task Transfers

**Request:**
```json
POST /seasonal-management/farming-plan-task/env/
{
  "name": "test-pour-task-001",
  "farming_plan_state": "state-123",
  "task_type": "ENV_POUR",
  "label": "Updated Test Pour Task",
  "description": "Updated ENV_POUR task with modified transfers",
  "task_transfers": [
    {
      "source_task_id": "test-task-002",
      "source_task_type": "ENV_STEAM_POT",
      "description": "Transfer from steam pot task"
    }
  ],
  "item_list": [
    {
      "name": "pour-item-001",
      "quantity": 8,
      "description": "Updated pour item 1",
      "iot_category_id": "category-789",
      "active_uom": "kg",
      "active_conversion_factor": 1,
      "draft_quantity": 4
    }
  ]
}
```

**Expected Result:**
- Task transfers updated (old transfer deleted, new one created)
- pour-item-001 updated

## Verification Steps

1. **Check Database State:** After each test, verify the database contains the expected records
2. **Verify Audit Fields:** Ensure `modified`, `modified_by` fields are updated correctly
3. **Check Cascading Deletes:** Verify that deleted items are properly removed from database
4. **Transaction Integrity:** Ensure that failed operations roll back completely
5. **API Response:** Verify the API returns the complete task with all related data

## Error Scenarios to Test

1. **Invalid Task Type for Transfers:** Try to create task transfers for non-ENV_POUR tasks
2. **Non-existent Source Task:** Reference a source task that doesn't exist in task_transfers
3. **Database Constraint Violations:** Test with invalid foreign key references
4. **Transaction Rollback:** Simulate database errors to ensure proper rollback

## Performance Considerations

- Test with large arrays of items (100+ items) to verify performance
- Monitor database query count to ensure efficient operations
- Test concurrent upsert operations on the same task
